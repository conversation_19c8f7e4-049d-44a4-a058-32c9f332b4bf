name: "❤️‍🔥ᴬᴳᴱᴺᵀ Agent scenario request"
description: Propose a agent scenario request for RAGFlow.
title: "[Agent Scenario Request]: "
labels: ["❤️‍🔥ᴬᴳᴱᴺᵀ agent scenario"]
body:
  - type: checkboxes
    attributes:
      label: Self Checks
      description: "Please check the following in order to be responded in time :)"
      options:
        - label: I have searched for existing issues [search for existing issues](https://github.com/infiniflow/ragflow/issues), including closed ones.
          required: true
        - label: I confirm that I am using English to submit this report ([Language Policy](https://github.com/infiniflow/ragflow/issues/5910)).
          required: true
        - label: Non-english title submitions will be closed directly ( 非英文标题的提交将会被直接关闭 ) ([Language Policy](https://github.com/infiniflow/ragflow/issues/5910)).
          required: true
        - label: "Please do not modify this template :) and fill in all the required fields."
          required: true
  - type: textarea
    attributes:
      label: Is your feature request related to a scenario?
      description: |
        A clear and concise description of what the scenario is. Ex. I'm always frustrated when [...]
      render: Markdown
    validations:
      required: false
  - type: textarea
    attributes:
      label: Describe the feature you'd like
      description: A clear and concise description of what you want to happen.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Documentation, adoption, use case
      description: If you can, explain some scenarios how users might use this, situations it would be helpful in. Any API designs, mockups, or diagrams are also helpful.
      render: Markdown
    validations:
      required: false
  - type: textarea
    attributes:
      label: Additional information
      description: |
        Add any other context or screenshots about the feature request here.
    validations:
      required: false