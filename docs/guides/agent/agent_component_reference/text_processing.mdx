---
sidebar_position: 15
slug: /text_processing
---

# Text processing component

A component that merges or splits texts.

---

A **Text processing** component merges or splits texts.

## Configurations

### Method

- Split: Split the text
- Merge: Merge the text

### Split_ref

Appears only when you select **Split** as method.

The variable to be split. Type `/` to quickly insert variables.

### Script 

Template for the merge. Appears only when you select **Merge** as method. Type `/` to quickly insert variables.

### Delimiters

The delimiter(s) used to split or merge the text.

### Output

The global variable name for the output of the component, which can be referenced by other components in the workflow.

