---
sidebar_position: 4
slug: /message_component
---

# Message component

A component that sends out a static or dynamic message.

---

As the final component of the workflow, a Message component returns the workflow’s ultimate data output accompanied by predefined message content. The system selects one message at random if multiple messages are provided.

## Configurations

### Messages

The message to send out. Click `(x)` or type `/` to quickly insert variables.

Click **+ Add message** to add message options. When multiple messages are supplied, the **Message** component randomly selects one to send.

