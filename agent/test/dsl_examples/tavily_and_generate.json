{"components": {"begin": {"obj": {"component_name": "<PERSON><PERSON>", "params": {"prologue": "Hi there!"}}, "downstream": ["tavily:0"], "upstream": []}, "tavily:0": {"obj": {"component_name": "<PERSON><PERSON>Sear<PERSON>", "params": {"api_key": "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"}}, "downstream": ["generate:0"], "upstream": ["begin"]}, "generate:0": {"obj": {"component_name": "LLM", "params": {"llm_id": "deepseek-chat", "sys_prompt": "You are an intelligent assistant. Please summarize the content of the knowledge base to answer the question. Please list the data in the knowledge base and answer in detail. When all knowledge base content is irrelevant to the question, your answer must include the sentence \"The answer you are looking for is not found in the knowledge base!\" Answers need to consider chat history.\n      Here is the knowledge base:\n      {tavily:0@formalized_content}\n      The above is the knowledge base.", "temperature": 0.2}}, "downstream": ["message:0"], "upstream": ["tavily:0"]}, "message:0": {"obj": {"component_name": "Message", "params": {"content": ["{generate:0@content}"]}}, "downstream": [], "upstream": ["generate:0"]}}, "history": [], "path": [], "retrival": {"chunks": [], "doc_aggs": []}, "globals": {"sys.query": "", "sys.user_id": "", "sys.conversation_turns": 0, "sys.files": []}}