{"components": {"begin": {"obj": {"component_name": "<PERSON><PERSON>", "params": {"prologue": "Hi there!"}}, "downstream": ["generate:0"], "upstream": []}, "generate:0": {"obj": {"component_name": "Agent", "params": {"llm_id": "deepseek-chat", "sys_prompt": "You are an helpful research assistant. \nPlease decompose user's topic: '{sys.query}' into several meaningful sub-topics. \nThe output format MUST be an string array like: [\"sub-topic1\", \"sub-topic2\", ...]. Redundant information is forbidden.", "temperature": 0.2, "cite": false, "output_structure": ["sub-topic1", "sub-topic2", "sub-topic3"]}}, "downstream": ["iteration:0"], "upstream": ["begin"]}, "iteration:0": {"obj": {"component_name": "Iteration", "params": {"items_ref": "generate:0@structured_content"}}, "downstream": ["message:0"], "upstream": ["generate:0"]}, "iterationitem:0": {"obj": {"component_name": "IterationItem", "params": {}}, "parent_id": "iteration:0", "downstream": ["tavily:0"], "upstream": []}, "tavily:0": {"obj": {"component_name": "<PERSON><PERSON>Sear<PERSON>", "params": {"api_key": "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", "query": "iterationitem:0@result"}}, "parent_id": "iteration:0", "downstream": ["generate:1"], "upstream": ["iterationitem:0"]}, "generate:1": {"obj": {"component_name": "Agent", "params": {"llm_id": "deepseek-chat", "sys_prompt": "Your goal is to provide answers based on information from the internet. \nYou must use the provided search results to find relevant online information. \nYou should never use your own knowledge to answer questions.\nPlease include relevant url sources in the end of your answers.\n\n \"{tavily:0@formalized_content}\" \nUsing the above information, answer the following question or topic: \"{iterationitem:0@result} \"\nin a detailed report — The report should focus on the answer to the question, should be well structured, informative, in depth, with facts and numbers if available, a minimum of 200 words and with markdown syntax and apa format. Write all source urls at the end of the report in apa format. You should write your report only based on the given information and nothing else.", "temperature": 0.9, "cite": false}}, "parent_id": "iteration:0", "downstream": ["iterationitem:0"], "upstream": ["tavily:0"]}, "message:0": {"obj": {"component_name": "Message", "params": {"content": ["{iteration:0@generate:1}"]}}, "downstream": [], "upstream": ["iteration:0"]}}, "history": [], "path": [], "retrival": {"chunks": [], "doc_aggs": []}, "globals": {"sys.query": "", "sys.user_id": "", "sys.conversation_turns": 0, "sys.files": []}}