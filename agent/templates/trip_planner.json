{"id": 14, "title": "<PERSON>ner", "description": "This smart trip planner utilizes LLM technology to automatically generate customized travel itineraries, with optional tool integration for enhanced reliability.", "canvas_type": "Consumer App", "dsl": {"components": {"Agent:OddGuestsPump": {"downstream": ["Agent:RichTermsCamp"], "obj": {"component_name": "Agent", "params": {"delay_after_error": 1, "description": "", "exception_comment": "", "exception_default_value": "", "exception_goto": [], "exception_method": null, "frequencyPenaltyEnabled": false, "frequency_penalty": 0.7, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_retries": 3, "max_rounds": 5, "max_tokens": 256, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}}, "presencePenaltyEnabled": false, "presence_penalty": 0.4, "prompts": [{"content": "User's query:\n{sys.query}", "role": "user"}], "sys_prompt": "Role: Professional tour guide: Create detailed travel plans per user needs.​\nFirst, specify departure location, destination, and travel duration (for subsequent agents to retrieve).​\nDevelop the plan using tools to get real-time weather, holidays, attraction hours, traffic, etc. Adjust itinerary accordingly (e.g., reschedule outdoor activities on rainy days) to ensure practicality, efficiency, and alignment with user preferences.​\nFor real-time info retrieval, only output tool-returned content and pass it to subsequent agents; never rely on your own knowledge base.​\n", "temperature": 0.1, "temperatureEnabled": true, "tools": [], "topPEnabled": false, "top_p": 0.3, "user_prompt": "", "visual_files_var": ""}}, "upstream": ["begin"]}, "Agent:RichTermsCamp": {"downstream": ["Agent:WeakCarrotsTan"], "obj": {"component_name": "Agent", "params": {"delay_after_error": 1, "description": "", "exception_comment": "", "exception_default_value": "", "exception_goto": [], "exception_method": null, "frequencyPenaltyEnabled": false, "frequency_penalty": 0.7, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_retries": 3, "max_rounds": 5, "max_tokens": 256, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}}, "presencePenaltyEnabled": false, "presence_penalty": 0.4, "prompts": [{"content": "User's query:\n{sys.query}\n\nFirst step result:\n{Agent:OddGuestsPump@content}", "role": "user"}], "sys_prompt": "You are a Transit & Stay Agent, collaborating with upstream planners.\n\n Use tools to retrieve real-time info for transportation (flights, trains, rentals, etc.) and accommodation (hotels, rentals, etc.) based on the itinerary. Recommend options matching dates, destinations, budgets, and preferences, adjusting for availability or conflicts to align with the overall plan.", "temperature": 0.1, "temperatureEnabled": true, "tools": [{"component_name": "<PERSON><PERSON>Sear<PERSON>", "name": "<PERSON><PERSON>Sear<PERSON>", "params": {"api_key": "", "days": 7, "exclude_domains": [], "include_answer": false, "include_domains": [], "include_image_descriptions": false, "include_images": false, "include_raw_content": true, "max_results": 5, "outputs": {"formalized_content": {"type": "string", "value": ""}, "json": {"type": "Array<Object>", "value": []}}, "query": "sys.query", "search_depth": "basic", "topic": "general"}}, {"component_name": "TavilyExtract", "name": "TavilyExtract", "params": {"api_key": ""}}], "topPEnabled": false, "top_p": 0.3, "user_prompt": "", "visual_files_var": ""}}, "upstream": ["Agent:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "Agent:WeakCarrotsTan": {"downstream": ["Message:ThickEyesUnite"], "obj": {"component_name": "Agent", "params": {"delay_after_error": 1, "description": "", "exception_comment": "", "exception_default_value": "", "exception_goto": [], "exception_method": null, "frequencyPenaltyEnabled": false, "frequency_penalty": 0.7, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_retries": 3, "max_rounds": 5, "max_tokens": 256, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}}, "presencePenaltyEnabled": false, "presence_penalty": 0.4, "prompts": [{"content": "User's query:\n{sys.query}\n\nTravel plan:\n{Agent:OddGuestsPump@content}\n\nTransit & Stay plan:\n{Agent:RichTermsCamp@content}", "role": "user"}], "sys_prompt": "You are a Result Generator. \nYour task is to produce accurate and reliable travel plans based on integrated information from upstream agents and tool-retrieved data. Ensure the final plan is logically structured, time-efficient, and consistent with all verified details—including clear timelines, confirmed transportation/accommodation arrangements, and practical activity adjustments . Prioritize clarity and feasibility to help users execute the plan smoothly.", "temperature": 0.1, "temperatureEnabled": true, "tools": [], "topPEnabled": false, "top_p": 0.3, "user_prompt": "", "visual_files_var": ""}}, "upstream": ["Agent:RichTermsCamp"]}, "Message:ThickEyesUnite": {"downstream": [], "obj": {"component_name": "Message", "params": {"content": ["{Agent:WeakCarrotsTan@content}"]}}, "upstream": ["Agent:WeakCarrotsTan"]}, "begin": {"downstream": ["Agent:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "obj": {"component_name": "<PERSON><PERSON>", "params": {"enablePrologue": true, "inputs": {}, "mode": "conversational", "prologue": "Hi!  I’m here to help plan your trip. Any destination in mind?"}}, "upstream": []}}, "globals": {"sys.conversation_turns": 0, "sys.files": [], "sys.query": "", "sys.user_id": ""}, "graph": {"edges": [{"data": {"isHovered": false}, "id": "xy-edge__beginstart-Agent:OddGuestsPumpend", "source": "begin", "sourceHandle": "start", "target": "Agent:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "targetHandle": "end"}, {"data": {"isHovered": false}, "id": "xy-edge__Agent:OddGuestsPumpstart-Agent:RichTermsCampend", "source": "Agent:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceHandle": "start", "target": "Agent:RichTermsCamp", "targetHandle": "end"}, {"data": {"isHovered": false}, "id": "xy-edge__Agent:RichTermsCampstart-Agent:WeakCarrotsTanend", "source": "Agent:RichTermsCamp", "sourceHandle": "start", "target": "Agent:WeakCarrotsTan", "targetHandle": "end"}, {"data": {"isHovered": false}, "id": "xy-edge__Agent:WeakCarrotsTanstart-Message:ThickEyesUniteend", "source": "Agent:WeakCarrotsTan", "sourceHandle": "start", "target": "Message:ThickEyesUnite", "targetHandle": "end"}, {"data": {"isHovered": false}, "id": "xy-edge__Agent:RichTermsCamptool-Tool:BreezyStreetsHuntend", "source": "Agent:RichTermsCamp", "sourceHandle": "tool", "target": "Tool:<PERSON>zyStreetsHunt", "targetHandle": "end"}], "nodes": [{"data": {"form": {"enablePrologue": true, "inputs": {}, "mode": "conversational", "prologue": "Hi!  I’m here to help plan your trip. Any destination in mind?"}, "label": "<PERSON><PERSON>", "name": "begin"}, "dragging": false, "id": "begin", "measured": {"height": 48, "width": 200}, "position": {"x": 333.3224354104293, "y": -31.71751112667888}, "selected": false, "sourcePosition": "left", "targetPosition": "right", "type": "beginNode"}, {"data": {"form": {"delay_after_error": 1, "description": "", "exception_comment": "", "exception_default_value": "", "exception_goto": [], "exception_method": null, "frequencyPenaltyEnabled": false, "frequency_penalty": 0.7, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_retries": 3, "max_rounds": 5, "max_tokens": 256, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}}, "presencePenaltyEnabled": false, "presence_penalty": 0.4, "prompts": [{"content": "User's query:\n{sys.query}", "role": "user"}], "sys_prompt": "Role: Professional tour guide: Create detailed travel plans per user needs.​\nFirst, specify departure location, destination, and travel duration (for subsequent agents to retrieve).​\nDevelop the plan using tools to get real-time weather, holidays, attraction hours, traffic, etc. Adjust itinerary accordingly (e.g., reschedule outdoor activities on rainy days) to ensure practicality, efficiency, and alignment with user preferences.​\nFor real-time info retrieval, only output tool-returned content and pass it to subsequent agents; never rely on your own knowledge base.​\n", "temperature": 0.1, "temperatureEnabled": true, "tools": [], "topPEnabled": false, "top_p": 0.3, "user_prompt": "", "visual_files_var": ""}, "label": "Agent", "name": "Travel Planning Agent"}, "dragging": false, "id": "Agent:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "measured": {"height": 84, "width": 200}, "position": {"x": 636.3704165924755, "y": -48.48140762793254}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "agentNode"}, {"data": {"form": {"delay_after_error": 1, "description": "", "exception_comment": "", "exception_default_value": "", "exception_goto": [], "exception_method": null, "frequencyPenaltyEnabled": false, "frequency_penalty": 0.7, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_retries": 3, "max_rounds": 5, "max_tokens": 256, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}}, "presencePenaltyEnabled": false, "presence_penalty": 0.4, "prompts": [{"content": "User's query:\n{sys.query}\n\nFirst step result:\n{Agent:OddGuestsPump@content}", "role": "user"}], "sys_prompt": "You are a Transit & Stay Agent, collaborating with upstream planners.\n\n Use tools to retrieve real-time info for transportation (flights, trains, rentals, etc.) and accommodation (hotels, rentals, etc.) based on the itinerary. Recommend options matching dates, destinations, budgets, and preferences, adjusting for availability or conflicts to align with the overall plan.", "temperature": 0.1, "temperatureEnabled": true, "tools": [{"component_name": "<PERSON><PERSON>Sear<PERSON>", "name": "<PERSON><PERSON>Sear<PERSON>", "params": {"api_key": "", "days": 7, "exclude_domains": [], "include_answer": false, "include_domains": [], "include_image_descriptions": false, "include_images": false, "include_raw_content": true, "max_results": 5, "outputs": {"formalized_content": {"type": "string", "value": ""}, "json": {"type": "Array<Object>", "value": []}}, "query": "sys.query", "search_depth": "basic", "topic": "general"}}, {"component_name": "TavilyExtract", "name": "TavilyExtract", "params": {"api_key": ""}}], "topPEnabled": false, "top_p": 0.3, "user_prompt": "", "visual_files_var": ""}, "label": "Agent", "name": "Transit & Stay Agent"}, "id": "Agent:RichTermsCamp", "measured": {"height": 84, "width": 200}, "position": {"x": 936.3704165924755, "y": -48.48140762793254}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "agentNode"}, {"data": {"form": {"delay_after_error": 1, "description": "", "exception_comment": "", "exception_default_value": "", "exception_goto": [], "exception_method": null, "frequencyPenaltyEnabled": false, "frequency_penalty": 0.7, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_retries": 3, "max_rounds": 5, "max_tokens": 256, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}}, "presencePenaltyEnabled": false, "presence_penalty": 0.4, "prompts": [{"content": "User's query:\n{sys.query}\n\nTravel plan:\n{Agent:OddGuestsPump@content}\n\nTransit & Stay plan:\n{Agent:RichTermsCamp@content}", "role": "user"}], "sys_prompt": "You are a Result Generator. \nYour task is to produce accurate and reliable travel plans based on integrated information from upstream agents and tool-retrieved data. Ensure the final plan is logically structured, time-efficient, and consistent with all verified details—including clear timelines, confirmed transportation/accommodation arrangements, and practical activity adjustments . Prioritize clarity and feasibility to help users execute the plan smoothly.", "temperature": 0.1, "temperatureEnabled": true, "tools": [], "topPEnabled": false, "top_p": 0.3, "user_prompt": "", "visual_files_var": ""}, "label": "Agent", "name": "Result Generator"}, "dragging": false, "id": "Agent:WeakCarrotsTan", "measured": {"height": 84, "width": 200}, "position": {"x": 1236.3704165924755, "y": -48.48140762793254}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "agentNode"}, {"data": {"form": {"content": ["{Agent:WeakCarrotsTan@content}"]}, "label": "Message", "name": "Final Plan"}, "dragging": false, "id": "Message:ThickEyesUnite", "measured": {"height": 56, "width": 200}, "position": {"x": 1583.2969941480576, "y": -26.582338101994175}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "messageNode"}, {"data": {"form": {"text": "The Agent will create detailed travel plans per user needs.\n​Add a map tool（eg. amap MCP） to this Agent for more reliable results."}, "label": "Note", "name": "Note: Travel Planning Agent"}, "dragHandle": ".note-drag-handle", "dragging": false, "id": "Note:GentleLlamasShake", "measured": {"height": 136, "width": 244}, "position": {"x": 628.3550234247459, "y": -226.23395345704375}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "noteNode"}, {"data": {"form": {"text": "The Agent will use tools to retrieve real-time info for transportation and accommodation."}, "label": "Note", "name": "Note: Transit & Stay Agent"}, "dragHandle": ".note-drag-handle", "dragging": false, "id": "Note:ClearLlamasTell", "measured": {"height": 136, "width": 244}, "position": {"x": 942.4779236864392, "y": -224.44816237892894}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "noteNode"}, {"data": {"form": {"description": "This is an agent for a specific task.", "user_prompt": "This is the order you need to send to the agent."}, "label": "Tool", "name": "flow.tool_0"}, "id": "Tool:<PERSON>zyStreetsHunt", "measured": {"height": 84, "width": 200}, "position": {"x": 854.3704165924755, "y": 91.51859237206746}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "toolNode"}, {"data": {"form": {"text": "The Agent will produce accurate and reliable travel plans based on integrated information from upstream agents and tool-retrieved data. "}, "label": "Note", "name": "Note: Result Generator"}, "dragHandle": ".note-drag-handle", "dragging": false, "height": 169, "id": "Note:LongToysShine", "measured": {"height": 169, "width": 246}, "position": {"x": 1240.444738031005, "y": -242.8368862758842}, "resizing": false, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "noteNode", "width": 246}, {"data": {"form": {"text": "This workflow functions as your smart trip planner, utilizing LLM technology to automatically generate customized travel itineraries and featuring optional tool integration for enhanced reliability.\n"}, "label": "Note", "name": "Workflow Overall Description"}, "dragHandle": ".note-drag-handle", "dragging": false, "height": 183, "id": "Note:ProudPlanesMake", "measured": {"height": 183, "width": 284}, "position": {"x": 197.34345022177064, "y": -245.57788797841573}, "resizing": false, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "noteNode", "width": 284}]}, "history": [], "memory": [], "messages": [], "path": [], "retrieval": [], "task_id": "abf6ec5e6ddf11f0a28c047c16ec874f"}, "avatar": "data:image/png;base64,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"}