{"id": 12, "title": "Generate SEO Blog", "description": "This workflow automatically generates a complete SEO-optimized blog article based on a simple user input. You don’t need any writing experience. Just provide a topic or short request — the system will handle the rest.", "canvas_type": "Marketing", "dsl": {"components": {"Agent:BetterSitesSend": {"downstream": ["Agent:E<PERSON>NailsR<PERSON>in"], "obj": {"component_name": "Agent", "params": {"delay_after_error": 1, "description": "", "exception_comment": "", "exception_default_value": "", "exception_goto": [], "exception_method": null, "frequencyPenaltyEnabled": false, "frequency_penalty": 0.3, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_retries": 3, "max_rounds": 3, "max_tokens": 4096, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}}, "parameter": "Balance", "presencePenaltyEnabled": false, "presence_penalty": 0.2, "prompts": [{"content": "The parse and keyword agent output is {Agent:ClearRabbitsScream@content}", "role": "user"}], "sys_prompt": "# Role\n\nYou are the **Outline_Agent**, responsible for generating a clear and SEO-optimized blog outline based on the user's parsed writing intent and keyword strategy.\n\n# Tool Access:\n\n- You have access to a search tool called `Tavily Search`.\n\n- If you are unsure how to structure a section, you may call this tool to search for related blog outlines or content from Google.\n\n- Do not overuse it. Your job is to extract **structure**, not to write paragraphs.\n\n\n# Goals\n\n1. Create a well-structured outline with appropriate H2 and H3 headings.\n\n2. Ensure logical flow from introduction to conclusion.\n\n3. Assign 1–2 suggested long-tail keywords to each major section for SEO alignment.\n\n4. Make the structure suitable for downstream paragraph writing.\n\n\n\n\n#Note\n\n- Use concise, scannable section titles.\n\n- Do not write full paragraphs.\n\n- Prioritize clarity, logical progression, and SEO alignment.\n\n\n\n- If the blog type is “Tutorial” or “How-to”, include step-based sections.\n\n\n# Input\n\nYou will receive:\n\n- Writing Type (e.g., Tutorial, Informative Guide)\n\n- Target Audience\n\n- User Intent Summary\n\n- 3–5 long-tail keywords\n\n\nUse this information to design a structure that both informs readers and maximizes search engine visibility.\n\n# Output Format\n\n```markdown\n\n## Blog Title (suggested)\n\n[Give a short, SEO-friendly title suggestion]\n\n## Outline\n\n### Introduction\n\n- Purpose of the article\n\n- Brief context\n\n- **Suggested keywords**: [keyword1, keyword2]\n\n### H2: [Section Title 1]\n\n- [Short description of what this section will cover]\n\n- **Suggested keywords**: [keyword1, keyword2]\n\n### H2: [Section Title 2]\n\n- [Short description of what this section will cover]\n\n- **Suggested keywords**: [keyword1, keyword2]\n\n### H2: [Section Title 3]\n\n- [Optional H3 Subsection Title A]\n\n  - [Explanation of sub-point]\n\n- [Optional H3 Subsection Title B]\n\n  - [Explanation of sub-point]\n\n- **Suggested keywords**: [keyword1]\n\n### Conclusion\n\n- Recap key takeaways\n\n- Optional CTA (Call to Action)\n\n- **Suggested keywords**: [keyword3]\n\n", "temperature": 0.5, "temperatureEnabled": true, "tools": [{"component_name": "<PERSON><PERSON>Sear<PERSON>", "name": "<PERSON><PERSON>Sear<PERSON>", "params": {"api_key": "", "days": 7, "exclude_domains": [], "include_answer": false, "include_domains": [], "include_image_descriptions": false, "include_images": false, "include_raw_content": true, "max_results": 5, "outputs": {"formalized_content": {"type": "string", "value": ""}, "json": {"type": "Array<Object>", "value": []}}, "query": "sys.query", "search_depth": "basic", "topic": "general"}}], "topPEnabled": false, "top_p": 0.85, "user_prompt": "", "visual_files_var": ""}}, "upstream": ["Agent:ClearRabbitsScream"]}, "Agent:ClearRabbitsScream": {"downstream": ["Agent:BetterSitesSend"], "obj": {"component_name": "Agent", "params": {"delay_after_error": 1, "description": "", "exception_comment": "", "exception_default_value": "", "exception_goto": [], "exception_method": null, "frequencyPenaltyEnabled": false, "frequency_penalty": 0.5, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_retries": 3, "max_rounds": 1, "max_tokens": 4096, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}}, "parameter": "Precise", "presencePenaltyEnabled": false, "presence_penalty": 0.5, "prompts": [{"content": "The user query is {sys.query}", "role": "user"}], "sys_prompt": "# Role\n\nYou are the **Parse_And_Keyword_Agent**, responsible for interpreting a user's blog writing request and generating a structured writing intent summary and keyword strategy for SEO-optimized content generation.\n\n# Goals\n\n1. Extract and infer the user's true writing intent, even if the input is informal or vague.\n\n2. Identify the writing type, target audience, and implied goal.\n\n3. Suggest 3–5 long-tail keywords based on the input and context.\n\n4. Output all data in a Markdown format for downstream agents.\n\n# Operating Guidelines\n\n\n- If the user's input lacks clarity, make reasonable and **conservative** assumptions based on SEO best practices.\n\n- Always choose one clear \"Writing Type\" from the list below.\n\n- Your job is not to write the blog — only to structure the brief.\n\n# Output Format\n\n```markdown\n## Writing Type\n\n[Choose one: Tutorial / Informative Guide / Marketing Content / Case Study / Opinion Piece / How-to / Comparison Article]\n\n## Target Audience\n\n[Try to be specific based on clues in the input: e.g., marketing managers, junior developers, SEO beginners]\n\n## User Intent Summary\n\n[A 1–2 sentence summary of what the user wants to achieve with the blog post]\n\n## Suggested Long-tail Keywords\n\n- keyword 1\n\n- keyword 2\n\n- keyword 3\n\n- keyword 4 (optional)\n\n- keyword 5 (optional)\n\n\n\n\n## Input Examples (and how to handle them)\n\nInput: \"I want to write about RAGFlow.\"\n→ Output: Informative Guide, Audience: AI developers, Intent: explain what RAGFlow is and its use cases\n\nInput: \"Need a blog to promote our prompt design tool.\"\n→ Output: Marketing Content, Audience: product managers or tool adopters, Intent: raise awareness and interest in the product\n\n\n\nInput: \"How to get more Google traffic using AI\"\n→ Output: How-to, Audience: SEO marketers, Intent: guide readers on applying AI for SEO growth", "temperature": 0.2, "temperatureEnabled": true, "tools": [], "topPEnabled": false, "top_p": 0.75, "user_prompt": "", "visual_files_var": ""}}, "upstream": ["begin"]}, "Agent:EagerNailsRemain": {"downstream": ["Agent:LovelyHeadsOwn"], "obj": {"component_name": "Agent", "params": {"delay_after_error": 1, "description": "", "exception_comment": "", "exception_default_value": "", "exception_goto": [], "exception_method": null, "frequencyPenaltyEnabled": false, "frequency_penalty": 0.5, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_retries": 3, "max_rounds": 5, "max_tokens": 4096, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}}, "parameter": "Precise", "presencePenaltyEnabled": false, "presence_penalty": 0.5, "prompts": [{"content": "The parse and keyword agent output is {Agent:ClearRabbitsScream@content}\n\n\n\nThe Outline agent output is {Agent:BetterSitesSend@content}", "role": "user"}], "sys_prompt": "# Role\n\nYou are the **Body_Agent**, responsible for generating the full content of each section of an SEO-optimized blog based on the provided outline and keyword strategy.\n\n# Tool Access:\n\nYou can use the `Tavily Search` tool to retrieve relevant content, statistics, or examples to support each section you're writing.\n\nUse it **only** when the provided outline lacks enough information, or if the section requires factual grounding.\n\nAlways cite the original link or indicate source where possible.\n\n\n# Goals\n\n1. Write each section (based on H2/H3 structure) as a complete and natural blog paragraph.\n\n2. Integrate the suggested long-tail keywords naturally into each section.\n\n3. When appropriate, use the `Tavily Search` tool to enrich your writing with relevant facts, examples, or quotes.\n\n4. Ensure each section is clear, engaging, and informative, suitable for both human readers and search engines.\n\n\n# Style Guidelines\n\n- Write in a tone appropriate to the audience. Be explanatory, not promotional, unless it's a marketing blog.\n\n- Avoid generic filler content. Prioritize clarity, structure, and value.\n\n- Ensure SEO keywords are embedded seamlessly, not forcefully.\n\n\n\n- Maintain writing rhythm. Vary sentence lengths. Use transitions between ideas.\n\n\n# Input\n\n\nYou will receive:\n\n- Blog title\n\n- Structured outline (including section titles, keywords, and descriptions)\n\n- Target audience\n\n- Blog type and user intent\n\nYou must **follow the outline strictly**. Write content **section-by-section**, based on the structure.\n\n\n# Output Format\n\n```markdown\n\n## H2: [Section Title]\n\n[Your generated content for this section — 500-600 words, using keywords naturally.]\n\n", "temperature": 0.2, "temperatureEnabled": true, "tools": [{"component_name": "<PERSON><PERSON>Sear<PERSON>", "name": "<PERSON><PERSON>Sear<PERSON>", "params": {"api_key": "", "days": 7, "exclude_domains": [], "include_answer": false, "include_domains": [], "include_image_descriptions": false, "include_images": false, "include_raw_content": true, "max_results": 5, "outputs": {"formalized_content": {"type": "string", "value": ""}, "json": {"type": "Array<Object>", "value": []}}, "query": "sys.query", "search_depth": "basic", "topic": "general"}}], "topPEnabled": false, "top_p": 0.75, "user_prompt": "", "visual_files_var": ""}}, "upstream": ["Agent:BetterSitesSend"]}, "Agent:LovelyHeadsOwn": {"downstream": ["Message:LegalBeansBet"], "obj": {"component_name": "Agent", "params": {"delay_after_error": 1, "description": "", "exception_comment": "", "exception_default_value": "", "exception_goto": [], "exception_method": null, "frequencyPenaltyEnabled": false, "frequency_penalty": 0.5, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_retries": 3, "max_rounds": 5, "max_tokens": 4096, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}}, "parameter": "Precise", "presencePenaltyEnabled": false, "presence_penalty": 0.5, "prompts": [{"content": "The parse and keyword agent output is {Agent:ClearRabbitsScream@content}\n\nThe Outline agent output is {Agent:BetterSitesSend@content}\n\nThe Body agent output is {Agent:EagerNailsRemain@content}", "role": "user"}], "sys_prompt": "# Role\n\nYou are the **Editor_Agent**, responsible for finalizing the blog post for both human readability and SEO effectiveness.\n\n# Goals\n\n1. Polish the entire blog content for clarity, coherence, and style.\n\n2. Improve transitions between sections, ensure logical flow.\n\n3. Verify that keywords are used appropriately and effectively.\n\n4. Conduct a lightweight SEO audit — checking keyword density, structure (H1/H2/H3), and overall searchability.\n\n\n\n# Style Guidelines\n\n- Be precise. Avoid bloated or vague language.\n\n- Maintain an informative and engaging tone, suitable to the target audience.\n\n- Do not remove keywords unless absolutely necessary for clarity.\n\n- Ensure paragraph flow and section continuity.\n\n\n# Input\n\nYou will receive:\n\n- Full blog content, written section-by-section\n\n- Original outline with suggested keywords\n\n- Target audience and writing type\n\n# Output Format\n\n```markdown\n\n[The revised, fully polished blog post content goes here.]\n\n", "temperature": 0.2, "temperatureEnabled": true, "tools": [], "topPEnabled": false, "top_p": 0.75, "user_prompt": "", "visual_files_var": ""}}, "upstream": ["Agent:E<PERSON>NailsR<PERSON>in"]}, "Message:LegalBeansBet": {"downstream": [], "obj": {"component_name": "Message", "params": {"content": ["{Agent:LovelyHeadsOwn@content}"]}}, "upstream": ["Agent:LovelyHeadsOwn"]}, "begin": {"downstream": ["Agent:ClearRabbitsScream"], "obj": {"component_name": "<PERSON><PERSON>", "params": {"enablePrologue": true, "inputs": {}, "mode": "conversational", "prologue": "Hi! I'm your SEO blog assistant.\n\nTo get started, please tell me:\n1. What topic you want the blog to cover\n2. Who is the target audience\n3. What you hope to achieve with this blog (e.g., SEO traffic, teaching beginners, promoting a product)\n"}}, "upstream": []}}, "globals": {"sys.conversation_turns": 0, "sys.files": [], "sys.query": "", "sys.user_id": ""}, "graph": {"edges": [{"data": {"isHovered": false}, "id": "xy-edge__beginstart-Agent:ClearRabbitsScreamend", "source": "begin", "sourceHandle": "start", "target": "Agent:ClearRabbitsScream", "targetHandle": "end"}, {"data": {"isHovered": false}, "id": "xy-edge__Agent:ClearRabbitsScreamstart-Agent:BetterSitesSendend", "source": "Agent:ClearRabbitsScream", "sourceHandle": "start", "target": "Agent:BetterSitesSend", "targetHandle": "end"}, {"data": {"isHovered": false}, "id": "xy-edge__Agent:BetterSitesSendtool-Tool:SharpPensBurnend", "source": "Agent:BetterSitesSend", "sourceHandle": "tool", "target": "Tool:SharpPensBurn", "targetHandle": "end"}, {"data": {"isHovered": false}, "id": "xy-edge__Agent:BetterSitesSendstart-Agent:EagerNailsRemainend", "source": "Agent:BetterSitesSend", "sourceHandle": "start", "target": "Agent:E<PERSON>NailsR<PERSON>in", "targetHandle": "end"}, {"id": "xy-edge__Agent:EagerNailsRemaintool-Tool:WickedDeerHealend", "source": "Agent:E<PERSON>NailsR<PERSON>in", "sourceHandle": "tool", "target": "Tool:WickedDeerHeal", "targetHandle": "end"}, {"data": {"isHovered": false}, "id": "xy-edge__Agent:EagerNailsRemainstart-Agent:LovelyHeadsOwnend", "source": "Agent:E<PERSON>NailsR<PERSON>in", "sourceHandle": "start", "target": "Agent:LovelyHeadsOwn", "targetHandle": "end"}, {"data": {"isHovered": false}, "id": "xy-edge__Agent:LovelyHeadsOwnstart-Message:LegalBeansBetend", "source": "Agent:LovelyHeadsOwn", "sourceHandle": "start", "target": "Message:LegalBeansBet", "targetHandle": "end"}], "nodes": [{"data": {"form": {"enablePrologue": true, "inputs": {}, "mode": "conversational", "prologue": "Hi! I'm your SEO blog assistant.\n\nTo get started, please tell me:\n1. What topic you want the blog to cover\n2. Who is the target audience\n3. What you hope to achieve with this blog (e.g., SEO traffic, teaching beginners, promoting a product)\n"}, "label": "<PERSON><PERSON>", "name": "begin"}, "id": "begin", "measured": {"height": 48, "width": 200}, "position": {"x": 50, "y": 200}, "selected": false, "sourcePosition": "left", "targetPosition": "right", "type": "beginNode"}, {"data": {"form": {"delay_after_error": 1, "description": "", "exception_comment": "", "exception_default_value": "", "exception_goto": [], "exception_method": null, "frequencyPenaltyEnabled": false, "frequency_penalty": 0.5, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_retries": 3, "max_rounds": 1, "max_tokens": 4096, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}}, "parameter": "Precise", "presencePenaltyEnabled": false, "presence_penalty": 0.5, "prompts": [{"content": "The user query is {sys.query}", "role": "user"}], "sys_prompt": "# Role\n\nYou are the **Parse_And_Keyword_Agent**, responsible for interpreting a user's blog writing request and generating a structured writing intent summary and keyword strategy for SEO-optimized content generation.\n\n# Goals\n\n1. Extract and infer the user's true writing intent, even if the input is informal or vague.\n\n2. Identify the writing type, target audience, and implied goal.\n\n3. Suggest 3–5 long-tail keywords based on the input and context.\n\n4. Output all data in a Markdown format for downstream agents.\n\n# Operating Guidelines\n\n\n- If the user's input lacks clarity, make reasonable and **conservative** assumptions based on SEO best practices.\n\n- Always choose one clear \"Writing Type\" from the list below.\n\n- Your job is not to write the blog — only to structure the brief.\n\n# Output Format\n\n```markdown\n## Writing Type\n\n[Choose one: Tutorial / Informative Guide / Marketing Content / Case Study / Opinion Piece / How-to / Comparison Article]\n\n## Target Audience\n\n[Try to be specific based on clues in the input: e.g., marketing managers, junior developers, SEO beginners]\n\n## User Intent Summary\n\n[A 1–2 sentence summary of what the user wants to achieve with the blog post]\n\n## Suggested Long-tail Keywords\n\n- keyword 1\n\n- keyword 2\n\n- keyword 3\n\n- keyword 4 (optional)\n\n- keyword 5 (optional)\n\n\n\n\n## Input Examples (and how to handle them)\n\nInput: \"I want to write about RAGFlow.\"\n→ Output: Informative Guide, Audience: AI developers, Intent: explain what RAGFlow is and its use cases\n\nInput: \"Need a blog to promote our prompt design tool.\"\n→ Output: Marketing Content, Audience: product managers or tool adopters, Intent: raise awareness and interest in the product\n\n\n\nInput: \"How to get more Google traffic using AI\"\n→ Output: How-to, Audience: SEO marketers, Intent: guide readers on applying AI for SEO growth", "temperature": 0.2, "temperatureEnabled": true, "tools": [], "topPEnabled": false, "top_p": 0.75, "user_prompt": "", "visual_files_var": ""}, "label": "Agent", "name": "Parse And Keyword Agent"}, "dragging": false, "id": "Agent:ClearRabbitsScream", "measured": {"height": 84, "width": 200}, "position": {"x": 344.7766966202233, "y": 234.82202253184496}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "agentNode"}, {"data": {"form": {"delay_after_error": 1, "description": "", "exception_comment": "", "exception_default_value": "", "exception_goto": [], "exception_method": null, "frequencyPenaltyEnabled": false, "frequency_penalty": 0.3, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_retries": 3, "max_rounds": 3, "max_tokens": 4096, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}}, "parameter": "Balance", "presencePenaltyEnabled": false, "presence_penalty": 0.2, "prompts": [{"content": "The parse and keyword agent output is {Agent:ClearRabbitsScream@content}", "role": "user"}], "sys_prompt": "# Role\n\nYou are the **Outline_Agent**, responsible for generating a clear and SEO-optimized blog outline based on the user's parsed writing intent and keyword strategy.\n\n# Tool Access:\n\n- You have access to a search tool called `Tavily Search`.\n\n- If you are unsure how to structure a section, you may call this tool to search for related blog outlines or content from Google.\n\n- Do not overuse it. Your job is to extract **structure**, not to write paragraphs.\n\n\n# Goals\n\n1. Create a well-structured outline with appropriate H2 and H3 headings.\n\n2. Ensure logical flow from introduction to conclusion.\n\n3. Assign 1–2 suggested long-tail keywords to each major section for SEO alignment.\n\n4. Make the structure suitable for downstream paragraph writing.\n\n\n\n\n#Note\n\n- Use concise, scannable section titles.\n\n- Do not write full paragraphs.\n\n- Prioritize clarity, logical progression, and SEO alignment.\n\n\n\n- If the blog type is “Tutorial” or “How-to”, include step-based sections.\n\n\n# Input\n\nYou will receive:\n\n- Writing Type (e.g., Tutorial, Informative Guide)\n\n- Target Audience\n\n- User Intent Summary\n\n- 3–5 long-tail keywords\n\n\nUse this information to design a structure that both informs readers and maximizes search engine visibility.\n\n# Output Format\n\n```markdown\n\n## Blog Title (suggested)\n\n[Give a short, SEO-friendly title suggestion]\n\n## Outline\n\n### Introduction\n\n- Purpose of the article\n\n- Brief context\n\n- **Suggested keywords**: [keyword1, keyword2]\n\n### H2: [Section Title 1]\n\n- [Short description of what this section will cover]\n\n- **Suggested keywords**: [keyword1, keyword2]\n\n### H2: [Section Title 2]\n\n- [Short description of what this section will cover]\n\n- **Suggested keywords**: [keyword1, keyword2]\n\n### H2: [Section Title 3]\n\n- [Optional H3 Subsection Title A]\n\n  - [Explanation of sub-point]\n\n- [Optional H3 Subsection Title B]\n\n  - [Explanation of sub-point]\n\n- **Suggested keywords**: [keyword1]\n\n### Conclusion\n\n- Recap key takeaways\n\n- Optional CTA (Call to Action)\n\n- **Suggested keywords**: [keyword3]\n\n", "temperature": 0.5, "temperatureEnabled": true, "tools": [{"component_name": "<PERSON><PERSON>Sear<PERSON>", "name": "<PERSON><PERSON>Sear<PERSON>", "params": {"api_key": "", "days": 7, "exclude_domains": [], "include_answer": false, "include_domains": [], "include_image_descriptions": false, "include_images": false, "include_raw_content": true, "max_results": 5, "outputs": {"formalized_content": {"type": "string", "value": ""}, "json": {"type": "Array<Object>", "value": []}}, "query": "sys.query", "search_depth": "basic", "topic": "general"}}], "topPEnabled": false, "top_p": 0.85, "user_prompt": "", "visual_files_var": ""}, "label": "Agent", "name": "Outline Agent"}, "dragging": false, "id": "Agent:BetterSitesSend", "measured": {"height": 84, "width": 200}, "position": {"x": 613.4368763415628, "y": 164.3074269048589}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "agentNode"}, {"data": {"form": {"description": "This is an agent for a specific task.", "user_prompt": "This is the order you need to send to the agent."}, "label": "Tool", "name": "flow.tool_0"}, "dragging": false, "id": "Tool:SharpPensBurn", "measured": {"height": 44, "width": 200}, "position": {"x": 580.1877078861457, "y": 287.7669662022325}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "toolNode"}, {"data": {"form": {"delay_after_error": 1, "description": "", "exception_comment": "", "exception_default_value": "", "exception_goto": [], "exception_method": null, "frequencyPenaltyEnabled": false, "frequency_penalty": 0.5, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_retries": 3, "max_rounds": 5, "max_tokens": 4096, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}}, "parameter": "Precise", "presencePenaltyEnabled": false, "presence_penalty": 0.5, "prompts": [{"content": "The parse and keyword agent output is {Agent:ClearRabbitsScream@content}\n\n\n\nThe Outline agent output is {Agent:BetterSitesSend@content}", "role": "user"}], "sys_prompt": "# Role\n\nYou are the **Body_Agent**, responsible for generating the full content of each section of an SEO-optimized blog based on the provided outline and keyword strategy.\n\n# Tool Access:\n\nYou can use the `Tavily Search` tool to retrieve relevant content, statistics, or examples to support each section you're writing.\n\nUse it **only** when the provided outline lacks enough information, or if the section requires factual grounding.\n\nAlways cite the original link or indicate source where possible.\n\n\n# Goals\n\n1. Write each section (based on H2/H3 structure) as a complete and natural blog paragraph.\n\n2. Integrate the suggested long-tail keywords naturally into each section.\n\n3. When appropriate, use the `Tavily Search` tool to enrich your writing with relevant facts, examples, or quotes.\n\n4. Ensure each section is clear, engaging, and informative, suitable for both human readers and search engines.\n\n\n# Style Guidelines\n\n- Write in a tone appropriate to the audience. Be explanatory, not promotional, unless it's a marketing blog.\n\n- Avoid generic filler content. Prioritize clarity, structure, and value.\n\n- Ensure SEO keywords are embedded seamlessly, not forcefully.\n\n\n\n- Maintain writing rhythm. Vary sentence lengths. Use transitions between ideas.\n\n\n# Input\n\n\nYou will receive:\n\n- Blog title\n\n- Structured outline (including section titles, keywords, and descriptions)\n\n- Target audience\n\n- Blog type and user intent\n\nYou must **follow the outline strictly**. Write content **section-by-section**, based on the structure.\n\n\n# Output Format\n\n```markdown\n\n## H2: [Section Title]\n\n[Your generated content for this section — 500-600 words, using keywords naturally.]\n\n", "temperature": 0.2, "temperatureEnabled": true, "tools": [{"component_name": "<PERSON><PERSON>Sear<PERSON>", "name": "<PERSON><PERSON>Sear<PERSON>", "params": {"api_key": "", "days": 7, "exclude_domains": [], "include_answer": false, "include_domains": [], "include_image_descriptions": false, "include_images": false, "include_raw_content": true, "max_results": 5, "outputs": {"formalized_content": {"type": "string", "value": ""}, "json": {"type": "Array<Object>", "value": []}}, "query": "sys.query", "search_depth": "basic", "topic": "general"}}], "topPEnabled": false, "top_p": 0.75, "user_prompt": "", "visual_files_var": ""}, "label": "Agent", "name": "Body Agent"}, "dragging": false, "id": "Agent:E<PERSON>NailsR<PERSON>in", "measured": {"height": 84, "width": 200}, "position": {"x": 889.0614605692713, "y": 247.00973041799065}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "agentNode"}, {"data": {"form": {"description": "This is an agent for a specific task.", "user_prompt": "This is the order you need to send to the agent."}, "label": "Tool", "name": "flow.tool_1"}, "dragging": false, "id": "Tool:WickedDeerHeal", "measured": {"height": 44, "width": 200}, "position": {"x": 853.2006404239659, "y": 364.37541577229143}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "toolNode"}, {"data": {"form": {"delay_after_error": 1, "description": "", "exception_comment": "", "exception_default_value": "", "exception_goto": [], "exception_method": null, "frequencyPenaltyEnabled": false, "frequency_penalty": 0.5, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_retries": 3, "max_rounds": 5, "max_tokens": 4096, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}}, "parameter": "Precise", "presencePenaltyEnabled": false, "presence_penalty": 0.5, "prompts": [{"content": "The parse and keyword agent output is {Agent:ClearRabbitsScream@content}\n\nThe Outline agent output is {Agent:BetterSitesSend@content}\n\nThe Body agent output is {Agent:EagerNailsRemain@content}", "role": "user"}], "sys_prompt": "# Role\n\nYou are the **Editor_Agent**, responsible for finalizing the blog post for both human readability and SEO effectiveness.\n\n# Goals\n\n1. Polish the entire blog content for clarity, coherence, and style.\n\n2. Improve transitions between sections, ensure logical flow.\n\n3. Verify that keywords are used appropriately and effectively.\n\n4. Conduct a lightweight SEO audit — checking keyword density, structure (H1/H2/H3), and overall searchability.\n\n\n\n# Style Guidelines\n\n- Be precise. Avoid bloated or vague language.\n\n- Maintain an informative and engaging tone, suitable to the target audience.\n\n- Do not remove keywords unless absolutely necessary for clarity.\n\n- Ensure paragraph flow and section continuity.\n\n\n# Input\n\nYou will receive:\n\n- Full blog content, written section-by-section\n\n- Original outline with suggested keywords\n\n- Target audience and writing type\n\n# Output Format\n\n```markdown\n\n[The revised, fully polished blog post content goes here.]\n\n", "temperature": 0.2, "temperatureEnabled": true, "tools": [], "topPEnabled": false, "top_p": 0.75, "user_prompt": "", "visual_files_var": ""}, "label": "Agent", "name": "Editor Agent"}, "dragging": false, "id": "Agent:LovelyHeadsOwn", "measured": {"height": 84, "width": 200}, "position": {"x": 1160.3332919804993, "y": 149.50806732882472}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "agentNode"}, {"data": {"form": {"content": ["{Agent:LovelyHeadsOwn@content}"]}, "label": "Message", "name": "Response"}, "dragging": false, "id": "Message:LegalBeansBet", "measured": {"height": 56, "width": 200}, "position": {"x": 1370.6665839609984, "y": 267.0323933738015}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "messageNode"}, {"data": {"form": {"text": "This workflow automatically generates a complete SEO-optimized blog article based on a simple user input. You don’t need any writing experience. Just provide a topic or short request — the system will handle the rest.\n\nThe process includes the following key stages:\n\n1. **Understanding your topic and goals**\n2. **Designing the blog structure**\n3. **Writing high-quality content**\n\n\n"}, "label": "Note", "name": "Workflow Overall Description"}, "dragHandle": ".note-drag-handle", "dragging": false, "height": 205, "id": "Note:SlimyGhostsWear", "measured": {"height": 205, "width": 415}, "position": {"x": -284.3143151688742, "y": 150.47632147913419}, "resizing": false, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "noteNode", "width": 415}, {"data": {"form": {"text": "**Purpose**:  \nThis agent reads the user’s input and figures out what kind of blog needs to be written.\n\n**What it does**:\n- Understands the main topic you want to write about  \n- Identifies who the blog is for (e.g., beginners, marketers, developers)  \n- Determines the writing purpose (e.g., SEO traffic, product promotion, education)  \n- Suggests 3–5 long-tail SEO keywords related to the topic"}, "label": "Note", "name": "Parse And Keyword Agent"}, "dragHandle": ".note-drag-handle", "dragging": false, "height": 152, "id": "Note:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "measured": {"height": 152, "width": 340}, "position": {"x": 295.04147626768133, "y": 372.2755718118446}, "resizing": false, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "noteNode", "width": 340}, {"data": {"form": {"text": "**Purpose**:  \nThis agent builds the blog structure — just like writing a table of contents before you start writing the full article.\n\n**What it does**:\n- Suggests a clear blog title that includes important keywords  \n- Breaks the article into sections using H2 and H3 headings (like a professional blog layout)  \n- Assigns 1–2 recommended keywords to each section to help with SEO  \n- Follows the writing goal and target audience set in the previous step"}, "label": "Note", "name": "Outline Agent"}, "dragHandle": ".note-drag-handle", "dragging": false, "height": 146, "id": "Note:TallMelonsNotice", "measured": {"height": 146, "width": 343}, "position": {"x": 598.5644991893463, "y": 5.801054564756448}, "resizing": false, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "noteNode", "width": 343}, {"data": {"form": {"text": "**Purpose**:  \nThis agent is responsible for writing the actual content of the blog — paragraph by paragraph — based on the outline created earlier.\n\n**What it does**:\n- Looks at each H2/H3 section in the outline  \n- Writes 150–220 words of clear, helpful, and well-structured content per section  \n- Includes the suggested SEO keywords naturally (not keyword stuffing)  \n- Uses real examples or facts if needed (by calling a web search tool like <PERSON>ly)"}, "label": "Note", "name": "Body Agent"}, "dragHandle": ".note-drag-handle", "dragging": false, "height": 137, "id": "Note:RipeCougarsBuild", "measured": {"height": 137, "width": 319}, "position": {"x": 860.4854129814981, "y": 427.2196835690842}, "resizing": false, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "noteNode", "width": 319}, {"data": {"form": {"text": "**Purpose**:  \nThis agent reviews the entire blog draft to make sure it is smooth, professional, and SEO-friendly. It acts like a human editor before publishing.\n\n**What it does**:\n- Polishes the writing: improves sentence clarity, fixes awkward phrasing  \n- Makes sure the content flows well from one section to the next  \n- Double-checks keyword usage: are they present, natural, and not overused?  \n- Verifies the blog structure (H1, H2, H3 headings) is correct  \n- Adds two key SEO elements:\n  - **Meta Title** (shows up in search results)\n  - **Meta Description** (summary for Google and social sharing)"}, "label": "Note", "name": "Editor Agent"}, "dragHandle": ".note-drag-handle", "height": 146, "id": "Note:OpenTurkeysSell", "measured": {"height": 146, "width": 320}, "position": {"x": 1129, "y": -30}, "resizing": false, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "noteNode", "width": 320}]}, "history": [], "messages": [], "path": [], "retrieval": []}, "avatar": "data:image/jpeg;base64,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"}