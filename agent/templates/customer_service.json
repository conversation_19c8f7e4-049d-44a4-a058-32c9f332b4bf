{"id": 2, "title": "Multi-Agent Customer Support", "description": "This is a multi-agent system for intelligent customer service processing based on user intent classification. It uses the lead-agent to identify the type of user needs, assign tasks to sub-agents for processing.", "canvas_type": "Agent", "dsl": {"components": {"Agent:RottenRiversDo": {"downstream": ["Message:PurpleCitiesSee"], "obj": {"component_name": "Agent", "params": {"delay_after_error": 1, "description": "", "exception_comment": "", "exception_default_value": "", "exception_goto": [], "exception_method": null, "frequencyPenaltyEnabled": false, "frequency_penalty": 0.7, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_retries": 3, "max_rounds": 2, "max_tokens": 256, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}}, "presencePenaltyEnabled": false, "presence_penalty": 0.4, "prompts": [{"content": "The user query is {sys.query}", "role": "user"}], "sys_prompt": "# Role  \n\nYou are **Customer Server Agent**. Classify every user message; handle **contact** yourself. This is a multi-agent system.\n\n## Categories  \n\n1. **contact** – user gives phone, e‑mail, WeChat, Line, Discord, etc.  \n\n2. **casual** – small talk, not about the product.  \n\n3. **complain** – complaints or profanity about the product/service.  \n\n4. **product** – questions on product use, appearance, function, or errors.\n\n## If contact  \n\nReply with one random item below—do not change wording or call sub‑agents:  \n\n1. Okay, I've already written this down. What else can I do for you?  \n\n2. Got it. What else can I do for you?  \n\n3. Thanks for your trust! Our expert will contact you ASAP. Anything else I can help with?  \n\n4. Thanks! Anything else I can do for you?\n\n\n---\n\n\n## Otherwise (casual / complain / product)  \n\nLet Sub‑Agent returns its answer\n\n## Sub‑Agent \n\n- casual → **Casual Agent**  \nThis is an agent for handles casual conversationk.\n\n- complain → **Soothe Agent**  \nThis is an agent for handles complaints or emotional input.\n\n- product → **Product Agent** \nThis is an agent for handles product-related queries and can use the `Retrieval` tool.\n\n## Importance\n\n- When the Sub‑Agent returns its answer, forward that answer to the user verbatim — do not add, edit, or reason further.\n    ", "temperature": 0.1, "temperatureEnabled": true, "tools": [{"component_name": "Agent", "id": "Agent:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Casual Agent", "params": {"delay_after_error": 1, "description": "This is an agent for handles casual conversationk.", "exception_comment": "", "exception_default_value": "", "exception_goto": [], "exception_method": null, "frequencyPenaltyEnabled": false, "frequency_penalty": 0.3, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_retries": 1, "max_rounds": 1, "max_tokens": 4096, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}}, "parameter": "Balance", "presencePenaltyEnabled": false, "presence_penalty": 0.2, "prompts": [{"content": "{sys.query}", "role": "user"}], "sys_prompt": "You are a friendly and casual conversational assistant.  \n\nYour primary goal is to engage users in light and enjoyable daily conversation.  \n\n- Keep a natural, relaxed, and positive tone.  \n\n- Avoid sensitive, controversial, or negative topics.  \n\n- You may gently guide the conversation by introducing related casual topics if the user shows interest.  \n\n", "temperature": 0.5, "temperatureEnabled": true, "tools": [], "topPEnabled": false, "top_p": 0.85, "user_prompt": "This is the order you need to send to the agent.", "visual_files_var": ""}}, {"component_name": "Agent", "id": "Agent:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Soothe Agent", "params": {"delay_after_error": 1, "description": "This is an agent for handles complaints or emotional input.", "exception_comment": "", "exception_default_value": "", "exception_goto": [], "exception_method": null, "frequencyPenaltyEnabled": false, "frequency_penalty": 0.3, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_retries": 1, "max_rounds": 1, "max_tokens": 4096, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}}, "parameter": "Balance", "presencePenaltyEnabled": false, "presence_penalty": 0.2, "prompts": [{"content": "{sys.query}", "role": "user"}], "sys_prompt": "You are an empathetic mood-soothing assistant.  \n\nYour role is to comfort and encourage users when they feel upset or frustrated.  \n\n- Use a warm, kind, and understanding tone.  \n\n- Focus on showing empathy and emotional support rather than solving the problem directly.  \n\n- Always encourage users with positive and reassuring statements.  ", "temperature": 0.5, "temperatureEnabled": true, "tools": [], "topPEnabled": false, "top_p": 0.85, "user_prompt": "This is the order you need to send to the agent.", "visual_files_var": ""}}, {"component_name": "Agent", "id": "Agent:<PERSON><PERSON><PERSON><PERSON><PERSON>sRest", "name": "Product Agent", "params": {"delay_after_error": 1, "description": "This is an agent for handles product-related queries and can use the `Retrieval` tool.", "exception_comment": "", "exception_default_value": "", "exception_goto": [], "exception_method": null, "frequencyPenaltyEnabled": false, "frequency_penalty": 0.7, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_retries": 3, "max_rounds": 2, "max_tokens": 256, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}}, "presencePenaltyEnabled": false, "presence_penalty": 0.4, "prompts": [{"content": "{sys.query}", "role": "user"}], "sys_prompt": "# Role  \n\nYou are a Product Information Advisor with access to the **Retrieval** tool.\n\n# Workflow  \n\n1. Run **Retrieval** with a focused query from the user’s question.  \n\n2. Draft the reply **strictly** from the returned passages.  \n\n3. If nothing relevant is retrieved, reply:  \n\n   “I cannot find relevant documents in the knowledge base.”\n\n# Rules  \n\n- No assumptions, guesses, or extra‑KB knowledge.  \n\n- Factual, concise. Use bullets / numbers when helpful.  \n\n", "temperature": 0.1, "temperatureEnabled": true, "tools": [{"component_name": "Retrieval", "name": "Retrieval", "params": {"cross_languages": [], "description": "This is a product knowledge base", "empty_response": "", "kb_ids": [], "keywords_similarity_weight": 0.7, "outputs": {"formalized_content": {"type": "string", "value": ""}}, "rerank_id": "", "similarity_threshold": 0.2, "top_k": 1024, "top_n": 8, "use_kg": false}}], "topPEnabled": false, "top_p": 0.3, "user_prompt": "This is the order you need to send to the agent.", "visual_files_var": ""}}], "topPEnabled": false, "top_p": 0.3, "user_prompt": "", "visual_files_var": ""}}, "upstream": ["begin"]}, "Message:PurpleCitiesSee": {"downstream": [], "obj": {"component_name": "Message", "params": {"content": ["{Agent:RottenRiversDo@content}"]}}, "upstream": ["Agent:RottenRiversDo"]}, "begin": {"downstream": ["Agent:RottenRiversDo"], "obj": {"component_name": "<PERSON><PERSON>", "params": {"enablePrologue": true, "inputs": {}, "mode": "conversational", "prologue": "Hi! I'm an official AI customer service representative. How can I help you?"}}, "upstream": []}}, "globals": {"sys.conversation_turns": 0, "sys.files": [], "sys.query": "", "sys.user_id": ""}, "graph": {"edges": [{"data": {"isHovered": false}, "id": "xy-edge__beginstart-Agent:RottenRiversDoend", "source": "begin", "sourceHandle": "start", "target": "Agent:RottenRiversDo", "targetHandle": "end"}, {"data": {"isHovered": false}, "id": "xy-edge__Agent:RottenRiversDoagentBottom-Agent:SlowKiwisBehaveagentTop", "source": "Agent:RottenRiversDo", "sourceHandle": "agentBottom", "target": "Agent:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "targetHandle": "agentTop"}, {"data": {"isHovered": false}, "id": "xy-edge__Agent:RottenRiversDoagentBottom-Agent:PoorTaxesRescueagentTop", "source": "Agent:RottenRiversDo", "sourceHandle": "agentBottom", "target": "Agent:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "targetHandle": "agentTop"}, {"data": {"isHovered": false}, "id": "xy-edge__Agent:RottenRiversDoagentBottom-Agent:<PERSON><PERSON><PERSON><PERSON><PERSON>sRestagentTop", "source": "Agent:RottenRiversDo", "sourceHandle": "agentBottom", "target": "Agent:<PERSON><PERSON><PERSON><PERSON><PERSON>sRest", "targetHandle": "agentTop"}, {"data": {"isHovered": false}, "id": "xy-edge__Agent:Si<PERSON><PERSON><PERSON><PERSON>sResttool-Tool:CrazyS<PERSON>tsKissend", "source": "Agent:<PERSON><PERSON><PERSON><PERSON><PERSON>sRest", "sourceHandle": "tool", "target": "Tool:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "targetHandle": "end"}, {"data": {"isHovered": false}, "id": "xy-edge__Agent:RottenRiversDostart-Message:PurpleCitiesSeeend", "source": "Agent:RottenRiversDo", "sourceHandle": "start", "target": "Message:PurpleCitiesSee", "targetHandle": "end"}], "nodes": [{"data": {"form": {"enablePrologue": true, "inputs": {}, "mode": "conversational", "prologue": "Hi! I'm an official AI customer service representative. How can I help you?"}, "label": "<PERSON><PERSON>", "name": "begin"}, "id": "begin", "measured": {"height": 48, "width": 200}, "position": {"x": 50, "y": 200}, "selected": false, "sourcePosition": "left", "targetPosition": "right", "type": "beginNode"}, {"data": {"form": {"delay_after_error": 1, "description": "", "exception_comment": "", "exception_default_value": "", "exception_goto": [], "exception_method": null, "frequencyPenaltyEnabled": false, "frequency_penalty": 0.7, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_retries": 3, "max_rounds": 2, "max_tokens": 256, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}}, "presencePenaltyEnabled": false, "presence_penalty": 0.4, "prompts": [{"content": "The user query is {sys.query}", "role": "user"}], "sys_prompt": "# Role  \n\nYou are **Customer Server Agent**. Classify every user message; handle **contact** yourself. This is a multi-agent system.\n\n## Categories  \n\n1. **contact** – user gives phone, e‑mail, WeChat, Line, Discord, etc.  \n\n2. **casual** – small talk, not about the product.  \n\n3. **complain** – complaints or profanity about the product/service.  \n\n4. **product** – questions on product use, appearance, function, or errors.\n\n## If contact  \n\nReply with one random item below—do not change wording or call sub‑agents:  \n\n1. Okay, I've already written this down. What else can I do for you?  \n\n2. Got it. What else can I do for you?  \n\n3. Thanks for your trust! Our expert will contact you ASAP. Anything else I can help with?  \n\n4. Thanks! Anything else I can do for you?\n\n\n---\n\n\n## Otherwise (casual / complain / product)  \n\nLet Sub‑Agent returns its answer\n\n## Sub‑Agent \n\n- casual → **Casual Agent**  \nThis is an agent for handles casual conversationk.\n\n- complain → **Soothe Agent**  \nThis is an agent for handles complaints or emotional input.\n\n- product → **Product Agent** \nThis is an agent for handles product-related queries and can use the `Retrieval` tool.\n\n## Importance\n\n- When the Sub‑Agent returns its answer, forward that answer to the user verbatim — do not add, edit, or reason further.\n    ", "temperature": 0.1, "temperatureEnabled": true, "tools": [], "topPEnabled": false, "top_p": 0.3, "user_prompt": "", "visual_files_var": ""}, "label": "Agent", "name": "Customer Server Agent"}, "dragging": false, "id": "Agent:RottenRiversDo", "measured": {"height": 84, "width": 200}, "position": {"x": 350, "y": 198.88981333505626}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "agentNode"}, {"data": {"form": {"delay_after_error": 1, "description": "This is an agent for handles casual conversationk.", "exception_comment": "", "exception_default_value": "", "exception_goto": [], "exception_method": null, "frequencyPenaltyEnabled": false, "frequency_penalty": 0.3, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_retries": 1, "max_rounds": 1, "max_tokens": 4096, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}}, "parameter": "Balance", "presencePenaltyEnabled": false, "presence_penalty": 0.2, "prompts": [{"content": "{sys.query}", "role": "user"}], "sys_prompt": "You are a friendly and casual conversational assistant.  \n\nYour primary goal is to engage users in light and enjoyable daily conversation.  \n\n- Keep a natural, relaxed, and positive tone.  \n\n- Avoid sensitive, controversial, or negative topics.  \n\n- You may gently guide the conversation by introducing related casual topics if the user shows interest.  \n\n", "temperature": 0.5, "temperatureEnabled": true, "tools": [], "topPEnabled": false, "top_p": 0.85, "user_prompt": "This is the order you need to send to the agent.", "visual_files_var": ""}, "label": "Agent", "name": "Casual Agent"}, "dragging": false, "id": "Agent:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "measured": {"height": 84, "width": 200}, "position": {"x": 124.4782938105834, "y": 402.1704532368496}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "agentNode"}, {"data": {"form": {"delay_after_error": 1, "description": "This is an agent for handles complaints or emotional input.", "exception_comment": "", "exception_default_value": "", "exception_goto": [], "exception_method": null, "frequencyPenaltyEnabled": false, "frequency_penalty": 0.3, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_retries": 1, "max_rounds": 1, "max_tokens": 4096, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}}, "parameter": "Balance", "presencePenaltyEnabled": false, "presence_penalty": 0.2, "prompts": [{"content": "{sys.query}", "role": "user"}], "sys_prompt": "You are an empathetic mood-soothing assistant.  \n\nYour role is to comfort and encourage users when they feel upset or frustrated.  \n\n- Use a warm, kind, and understanding tone.  \n\n- Focus on showing empathy and emotional support rather than solving the problem directly.  \n\n- Always encourage users with positive and reassuring statements.  ", "temperature": 0.5, "temperatureEnabled": true, "tools": [], "topPEnabled": false, "top_p": 0.85, "user_prompt": "This is the order you need to send to the agent.", "visual_files_var": ""}, "label": "Agent", "name": "Soothe Agent"}, "dragging": false, "id": "Agent:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "measured": {"height": 84, "width": 200}, "position": {"x": 402.02090711979577, "y": 363.3139199638186}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "agentNode"}, {"data": {"form": {"delay_after_error": 1, "description": "This is an agent for handles product-related queries and can use the `Retrieval` tool.", "exception_comment": "", "exception_default_value": "", "exception_goto": [], "exception_method": null, "frequencyPenaltyEnabled": false, "frequency_penalty": 0.7, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_retries": 3, "max_rounds": 2, "max_tokens": 256, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}}, "presencePenaltyEnabled": false, "presence_penalty": 0.4, "prompts": [{"content": "{sys.query}", "role": "user"}], "sys_prompt": "# Role  \n\nYou are a Product Information Advisor with access to the **Retrieval** tool.\n\n# Workflow  \n\n1. Run **Retrieval** with a focused query from the user’s question.  \n\n2. Draft the reply **strictly** from the returned passages.  \n\n3. If nothing relevant is retrieved, reply:  \n\n   “I cannot find relevant documents in the knowledge base.”\n\n# Rules  \n\n- No assumptions, guesses, or extra‑KB knowledge.  \n\n- Factual, concise. Use bullets / numbers when helpful.  \n\n", "temperature": 0.1, "temperatureEnabled": true, "tools": [{"component_name": "Retrieval", "name": "Retrieval", "params": {"cross_languages": [], "description": "This is a product knowledge base", "empty_response": "", "kb_ids": [], "keywords_similarity_weight": 0.7, "outputs": {"formalized_content": {"type": "string", "value": ""}}, "rerank_id": "", "similarity_threshold": 0.2, "top_k": 1024, "top_n": 8, "use_kg": false}}], "topPEnabled": false, "top_p": 0.3, "user_prompt": "This is the order you need to send to the agent.", "visual_files_var": ""}, "label": "Agent", "name": "Product Agent"}, "dragging": false, "id": "Agent:<PERSON><PERSON><PERSON><PERSON><PERSON>sRest", "measured": {"height": 84, "width": 200}, "position": {"x": 684.0042670887832, "y": 317.79626670112515}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "agentNode"}, {"data": {"form": {"description": "This is an agent for a specific task.", "user_prompt": "This is the order you need to send to the agent."}, "label": "Tool", "name": "flow.tool_0"}, "dragging": false, "id": "Tool:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "measured": {"height": 48, "width": 200}, "position": {"x": 659.7339736658578, "y": 443.3638400568565}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "toolNode"}, {"data": {"form": {"content": ["{Agent:RottenRiversDo@content}"]}, "label": "Message", "name": "Response"}, "dragging": false, "id": "Message:PurpleCitiesSee", "measured": {"height": 56, "width": 200}, "position": {"x": 675.534293293706, "y": 158.92309339708154}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "messageNode"}, {"data": {"form": {"text": "This is a multi-agent system for intelligent customer service processing based on user intent classification. It uses the lead-agent to identify the type of user needs, assign tasks to sub-agents for processing, and finally the lead agent outputs the results."}, "label": "Note", "name": "Workflow Overall Description"}, "dragHandle": ".note-drag-handle", "dragging": false, "height": 140, "id": "Note:<PERSON><PERSON><PERSON><PERSON><PERSON>", "measured": {"height": 140, "width": 385}, "position": {"x": -59.311679338397, "y": -2.2203733298874866}, "resizing": false, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "noteNode", "width": 385}, {"data": {"form": {"text": "Answers will be given strictly according to the content retrieved from the knowledge base."}, "label": "Note", "name": "Product Agent "}, "dragHandle": ".note-drag-handle", "dragging": false, "id": "Note:ColdCoinsBathe", "measured": {"height": 136, "width": 249}, "position": {"x": 994.4238924667025, "y": 329.08949370720796}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "noteNode"}]}, "history": [], "messages": [], "path": [], "retrieval": []}, "avatar": "data:image/png;base64,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"}