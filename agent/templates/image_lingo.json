{"id": 13, "title": "ImageLingo", "description": "ImageLingo lets you snap any photo containing text—menus, signs, or documents—and instantly recognize and translate it into your language of choice using advanced AI-powered translation technology.", "canvas_type": "Consumer App", "dsl": {"components": {"Agent:CoolPandasCrash": {"downstream": ["Message:CurlyApplesRelate"], "obj": {"component_name": "Agent", "params": {"delay_after_error": 1, "description": "", "exception_comment": "", "exception_goto": [], "exception_method": null, "frequencyPenaltyEnabled": false, "frequency_penalty": 0.7, "llm_filter": "image2text", "llm_id": "qwen-vl-plus@Tongyi-Qianwen", "maxTokensEnabled": false, "max_retries": 3, "max_rounds": 5, "max_tokens": 256, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}, "structured_output": {}}, "presencePenaltyEnabled": false, "presence_penalty": 0.4, "prompts": [{"content": "The user query is {sys.query}\n\n\n\nThe input files are {sys.files}\n\n", "role": "user"}], "sys_prompt": "You are a multilingual translation assistant that works from images. When given a photo of any text or scene, you should:\n\n\n\n1. Detect and extract all written text in the image, regardless of font, orientation, or style.  \n\n2. Identify the source language of the extracted text.  \n\n3. Determine the target language:\n\n   - If the user explicitly specifies a language, use that.\n\n   - If no language is specified, automatically detect the user’s spoken language and use that as the target.  \n\n4. Translate the content accurately into the target language, preserving meaning, tone, and formatting (e.g., line breaks, punctuation).  \n\n5. If the image contains signage, menus, labels, or other contextual text, adapt the translation to be natural and context-appropriate for daily use.  \n\n6. Return the translated text in plain, well-formatted paragraphs. If the user asks, also provide transliteration for non-Latin scripts.  \n\n7. If the image is unclear or the target language cannot be determined, ask a clarifying follow-up question.\n\n\nExample:\n\nUser: “Translate this photo for me.”\n\nAgent Input: [Image of a Japanese train schedule]\n\nAgent Output:\n\n“7:30 AM – 東京駅 (Tokyo Station)  \n\n8:15 AM – 新大阪 (Shin-Osaka)”  \n\n(Detected user language: English)```\n\n", "temperature": 0.1, "temperatureEnabled": true, "tools": [], "topPEnabled": false, "top_p": 0.3, "user_prompt": "", "visual_files_var": "sys.files"}}, "upstream": ["begin"]}, "Message:CurlyApplesRelate": {"downstream": [], "obj": {"component_name": "Message", "params": {"content": ["{Agent:<PERSON><PERSON>andasC<PERSON>@content}"]}}, "upstream": ["Agent:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "begin": {"downstream": ["Agent:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "obj": {"component_name": "<PERSON><PERSON>", "params": {"enablePrologue": true, "inputs": {}, "mode": "task", "prologue": "Hi there! I’m <PERSON><PERSON><PERSON><PERSON>, your on-the-go image translation assistant—just snap a photo, and I’ll instantly translate and adapt it into your language."}}, "upstream": []}}, "globals": {"sys.conversation_turns": 0, "sys.files": [], "sys.query": "", "sys.user_id": ""}, "graph": {"edges": [{"data": {"isHovered": false}, "id": "xy-edge__beginstart-Agent:CoolPandasCrashend", "source": "begin", "sourceHandle": "start", "target": "Agent:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "targetHandle": "end"}, {"data": {"isHovered": false}, "id": "xy-edge__Agent:CoolPandasCrashstart-Message:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>d", "source": "Agent:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceHandle": "start", "target": "Message:CurlyApplesRelate", "targetHandle": "end"}], "nodes": [{"data": {"form": {"enablePrologue": true, "inputs": {}, "mode": "task", "prologue": "Hi there! I’m <PERSON><PERSON><PERSON><PERSON>, your on-the-go image translation assistant—just snap a photo, and I’ll instantly translate and adapt it into your language."}, "label": "<PERSON><PERSON>", "name": "begin"}, "id": "begin", "measured": {"height": 48, "width": 200}, "position": {"x": 50, "y": 200}, "selected": false, "sourcePosition": "left", "targetPosition": "right", "type": "beginNode"}, {"data": {"form": {"delay_after_error": 1, "description": "", "exception_comment": "", "exception_goto": "", "exception_method": null, "frequencyPenaltyEnabled": false, "frequency_penalty": 0.7, "llm_filter": "image2text", "llm_id": "qwen-vl-plus@Tongyi-Qianwen", "maxTokensEnabled": false, "max_retries": 3, "max_rounds": 5, "max_tokens": 256, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}, "structured_output": {}}, "presencePenaltyEnabled": false, "presence_penalty": 0.4, "prompts": [{"content": "The user query is {sys.query}\n\n\n\nThe input files are {sys.files}\n\n", "role": "user"}], "sys_prompt": "You are a multilingual translation assistant that works from images. When given a photo of any text or scene, you should:\n\n\n\n1. Detect and extract all written text in the image, regardless of font, orientation, or style.  \n\n2. Identify the source language of the extracted text.  \n\n3. Determine the target language:\n\n   - If the user explicitly specifies a language, use that.\n\n   - If no language is specified, automatically detect the user’s spoken language and use that as the target.  \n\n4. Translate the content accurately into the target language, preserving meaning, tone, and formatting (e.g., line breaks, punctuation).  \n\n5. If the image contains signage, menus, labels, or other contextual text, adapt the translation to be natural and context-appropriate for daily use.  \n\n6. Return the translated text in plain, well-formatted paragraphs. If the user asks, also provide transliteration for non-Latin scripts.  \n\n7. If the image is unclear or the target language cannot be determined, ask a clarifying follow-up question.\n\n\nExample:\n\nUser: “Translate this photo for me.”\n\nAgent Input: [Image of a Japanese train schedule]\n\nAgent Output:\n\n“7:30 AM – 東京駅 (Tokyo Station)  \n\n8:15 AM – 新大阪 (Shin-Osaka)”  \n\n(Detected user language: English)```\n\n", "temperature": 0.1, "temperatureEnabled": true, "tools": [], "topPEnabled": false, "top_p": 0.3, "user_prompt": "", "visual_files_var": "sys.files"}, "label": "Agent", "name": "Translation Agent With Vision"}, "dragging": false, "id": "Agent:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "measured": {"height": 87, "width": 200}, "position": {"x": 350.5, "y": 200}, "selected": true, "sourcePosition": "right", "targetPosition": "left", "type": "agentNode"}, {"data": {"form": {"content": ["{Agent:<PERSON><PERSON>andasC<PERSON>@content}"]}, "label": "Message", "name": "Message"}, "id": "Message:CurlyApplesRelate", "measured": {"height": 56, "width": 200}, "position": {"x": 650, "y": 200}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "messageNode"}, {"data": {"form": {"text": "ImageLingo lets you snap any photo containing text—menus, signs, or documents—and instantly recognize and translate it into your language of choice using advanced OCR and AI-powered translation technology. With automatic source-language detection and context-aware adaptations, translations preserve formatting, tone, and intent. Your on-the-go language assistant. "}, "label": "Note", "name": "Translation Agent"}, "dragHandle": ".note-drag-handle", "dragging": false, "height": 190, "id": "Note:OpenCobrasMarry", "measured": {"height": 190, "width": 376}, "position": {"x": 385.5, "y": -42}, "resizing": false, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "noteNode", "width": 376}]}, "history": [], "messages": [], "path": [], "retrieval": []}, "avatar": "data:image/png;base64,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"}